<?php

use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File as File;
use Illuminate\Support\Facades\Mail;

\Illuminate\Support\Facades\Route::get('vimarketsfantasy', function () {
    return view('vi-markrts-fantasy');
});

\Illuminate\Support\Facades\Route::post('vimarketsfantasy', function (Request $request) {
    $data = $request->all();

    $dataPath = storage_path('app/fantasy.json');

// save to json file fantasy registered users
    if (!file_exists($dataPath)) {
        file_put_contents($dataPath, json_encode([$data], JSON_PRETTY_PRINT));
    } else {
        $json = json_decode(file_get_contents($dataPath), true) ?? [];
        $json[] = $data;
        file_put_contents($dataPath, json_encode($json, JSON_PRETTY_PRINT));
    }

    return view('vi-markrts-fantasy-success');
})->name('vi-markets-fantasy.register');

// registered users
\Illuminate\Support\Facades\Route::get('vimarketsfantasy/registered-users', function () {
    return json_decode(file_get_contents(storage_path('app/fantasy.json')));
});

// trade shares
Route::get('/vi-partnership', [
    'uses' => 'Website\ViPartnershipController@index',
    'as' => 'website.vi-partnership',
]);

Route::post('/vi-partnership', [
    'uses' => 'Website\ViPartnershipController@send',
    'as' => 'website.vi-partnership.send',
]);


Route::get('/trade-what-you-know', [
    'uses' => 'Website\TradeSharesController@index',
    'as' => 'website.trade_shares.index',
]);


Route::get('/trade-shares/users/{type?}', [
    'uses' => 'Website\TradeSharesController@users',
    'as' => 'website.trade_shares.users',
]);

Route::post('/trade-shares', [
    'uses' => 'Website\TradeSharesController@register',
    'as' => 'website.trade_shares.register',
]);

Route::get('/zoom-meeting', function () {

    return view('website.zoom_meeting');

});

Route::post('/zoom-meeting', function (\Illuminate\Http\Request $request) {

    \App\Models\ZoomMeeting::query()->create($request->all());

    return redirect()
        ->back()
        ->with('success', 'Thank you');

})->name('zoom_meeting_post');

Route::get('/zoom-meeting/participants', function () {

    $participants = \App\Models\ZoomMeeting::query()->latest()->paginate(20);

    return view('website.zoom_meeting_participants', compact('participants'));

})->middleware('auth');

Route::get('excel/export/{model}/{seminar_id}', [
    'uses' => 'Admin\ExcelController@export',
    'as' => 'excel.export'
]);

Route::group(['namespace' => 'Website'], function () {

    Route::get('/test', function () {

        $client = Client::query()->first();

        Mail::send('emails.new_client', ['client' => $client], function ($m) {

            $m->from(env('MAIL_USERNAME'), 'Vi seminars');

            $m->to('<EMAIL>')->subject('New Client Registered');


        });

    });


    Route::any('/corona-news', function (\Illuminate\Http\Request $request) {

        return view('corona_news');

    });


    Route::any('/vi-city-view', function (\Illuminate\Http\Request $request) {

        $viarabicnews = \Illuminate\Support\Facades\Storage::disk('viarabicnews');

        if ($request->getMethod() == 'POST' && $request->file('image_web') && $request->file('image_mobile')) {

            $web_name = now()->timestamp . '_web_' . uniqid() . '.' . $request->file('image_web')->getClientOriginalExtension();

            $viarabicnews->put(
                $web_name,
                File::get($request->file('image_web'))
            );

            $mobile_name = now()->timestamp . '_mobile_' . uniqid() . '.' . $request->file('image_mobile')->getClientOriginalExtension();

            $viarabicnews->put(
                $mobile_name,
                File::get($request->file('image_mobile'))
            );

        }

        $web_image = collect($viarabicnews->files())->last(function ($file) {
            return strpos($file, 'web') !== false;
        });

        $mobile_image = collect($viarabicnews->files())->last(function ($file) {
            return strpos($file, 'mobile') !== false;
        });

        return view('vi_arabic_news', compact('web_image', 'mobile_image'));

    });

    Route::any('/the-chartist', function (\Illuminate\Http\Request $request) {

        $the_chartist_driver = \Illuminate\Support\Facades\Storage::disk('the_chartist');

        if ($request->getMethod() == 'POST' && $request->file('image_web') && $request->file('image_mobile')) {

            $web_name = now()->timestamp . '_web_' . uniqid() . '.' . $request->file('image_web')->getClientOriginalExtension();

            $the_chartist_driver->put(
                $web_name,
                File::get($request->file('image_web'))
            );

            $mobile_name = now()->timestamp . '_mobile_' . uniqid() . '.' . $request->file('image_mobile')->getClientOriginalExtension();

            $the_chartist_driver->put(
                $mobile_name,
                File::get($request->file('image_mobile'))
            );

        }

        $web_image = collect($the_chartist_driver->files())->last(function ($file) {
            return strpos($file, 'web') !== false;
        });

        $mobile_image = collect($the_chartist_driver->files())->last(function ($file) {
            return strpos($file, 'mobile') !== false;
        });

        $disk = 'the_chartist';

        return view('the_chartist', compact('web_image', 'mobile_image', 'disk'));

    });

    Route::any('/the-chartist2', function (\Illuminate\Http\Request $request) {

        $the_chartist_driver = \Illuminate\Support\Facades\Storage::disk('the_chartist2');

        if ($request->getMethod() == 'POST' && $request->file('image_web') && $request->file('image_mobile')) {

            $web_name = now()->timestamp . '_web_' . uniqid() . '.' . $request->file('image_web')->getClientOriginalExtension();

            $the_chartist_driver->put(
                $web_name,
                File::get($request->file('image_web'))
            );

            $mobile_name = now()->timestamp . '_mobile_' . uniqid() . '.' . $request->file('image_mobile')->getClientOriginalExtension();

            $the_chartist_driver->put(
                $mobile_name,
                File::get($request->file('image_mobile'))
            );

        }

        $web_image = collect($the_chartist_driver->files())->last(function ($file) {
            return strpos($file, 'web') !== false;
        });

        $mobile_image = collect($the_chartist_driver->files())->last(function ($file) {
            return strpos($file, 'mobile') !== false;
        });

        $disk = 'the_chartist2';

        return view('the_chartist', compact('web_image', 'mobile_image', 'disk'));

    });

    Route::any('/the-chartist3', function (\Illuminate\Http\Request $request) {

        $the_chartist_driver = \Illuminate\Support\Facades\Storage::disk('the_chartist3');

        if ($request->getMethod() == 'POST' && $request->file('image_web') && $request->file('image_mobile')) {

            $web_name = now()->timestamp . '_web_' . uniqid() . '.' . $request->file('image_web')->getClientOriginalExtension();

            $the_chartist_driver->put(
                $web_name,
                File::get($request->file('image_web'))
            );

            $mobile_name = now()->timestamp . '_mobile_' . uniqid() . '.' . $request->file('image_mobile')->getClientOriginalExtension();

            $the_chartist_driver->put(
                $mobile_name,
                File::get($request->file('image_mobile'))
            );

        }

        $web_image = collect($the_chartist_driver->files())->last(function ($file) {
            return strpos($file, 'web') !== false;
        });

        $mobile_image = collect($the_chartist_driver->files())->last(function ($file) {
            return strpos($file, 'mobile') !== false;
        });

        $disk = 'the_chartist3';

        return view('the_chartist', compact('web_image', 'mobile_image', 'disk'));

    });

    Route::get('/eman', function () {


        $images = [
//            71 => 'http://viseminars.net/imgs/22.jpg',
//            72 => 'http://viseminars.net/imgs/26.jpg',
//            73 => 'http://viseminars.net/imgs/29.jpg',
//            74 => 'http://viseminars.net/imgs/IMG_1866.jpeg',
//            69 => 'http://viseminars.net/imgs/IMG_1865.jpeg',
//            70 => 'http://viseminars.net/imgs/IMG_1864.jpeg',
//            75 => 'http://viseminars.net/imgs/50+_dubai.jpg',
//            76 => 'http://viseminars.net/imgs/Dubi%20post-%D9%A1%D9%A3.jpg',
//            78 => 'http://viseminars.net/imgs/WhatsApp%20Image%202019-10-06%20at%2010.06.53%20AM.jpeg',
//            81 => 'http://viseminars.net/imgs/Artboard%201%20copy%206.jpg',
//            82 => 'http://viseminars.net/imgs/eman%20ayaf%20+50%20new%202%20link%20qatar.jpg',
        ];

        $seminars = collect([]);

        foreach ($images as $id => $image) {

            $seminars->push(\App\Models\Seminar::find($id));

        }

        return view('website.eman_ayaf', compact('seminars', 'images'));

    });

    Route::get('/bashar', function () {


        $images = [
            80 => 'http://viseminars.net/imgs/WhatsApp%20Image%202019-11-19%20at%2012.56.23%20PM.jpeg',
            79 => 'http://viseminars.net/imgs/WhatsApp%20Image%202019-11-19%20at%2012.56.22%20PM.jpeg',
        ];

        $seminars = collect([]);

        foreach ($images as $id => $image) {

            $seminars->push(\App\Models\Seminar::find($id));

        }

        return view('website.bashar', compact('seminars', 'images'));

    });

    Route::get('/eman/{date}', function ($date) {

        $seminars = \App\Models\Seminar::latest()->get();

        $seminar = null;

        $seminar_id = null;

        switch ($date) {

            case '22-aug':
                $seminar = \App\Models\Seminar::find(71);
                $seminar_id = 71;
                break;

            case '26-aug':
                $seminar = \App\Models\Seminar::find(72);
                $seminar_id = 72;
                break;

            case '29-aug':
                $seminar = \App\Models\Seminar::find(73);
                $seminar_id = 73;
                break;

            case '4-sep':
                $seminar = \App\Models\Seminar::find(74);
                $seminar_id = 74;
                break;

            case '11-sep':
                $seminar = \App\Models\Seminar::find(69);
                $seminar_id = 69;
                break;

            case '12-sep':
                $seminar = \App\Models\Seminar::find(70);
                $seminar_id = 70;
                break;

            case '16-sep':
                $seminar = \App\Models\Seminar::find(75);
                $seminar_id = 75;
                break;

            case '7-oct':
                $seminar = \App\Models\Seminar::find(76);
                $seminar_id = 76;
                break;


            case '20-nov':
                $seminar = \App\Models\Seminar::find(81);
                $seminar_id = 81;
                break;


            case '27-nov':
                $seminar = \App\Models\Seminar::find(82);
                $seminar_id = 82;
                break;

            case 'contact':
                $seminar = \App\Models\Seminar::find(53);
                $seminar_id = 53;
                break;

        }

        return view('website.eman_participants', compact('seminars', 'seminar', 'seminar_id'));

    });

    Route::get('/seminar/{seminar_id}', [
        'uses' => 'HomeController@seminar',
        'as' => 'seminar'
    ]);

    Route::post('participant/register', [
        'uses' => 'ParticipantController@register',
        'as' => 'participant_register'
    ]);

    Route::get('/7bars', [
        'uses' => 'CompetitionController@sevenBars',
        'as' => 'website.competition.7bars'
    ]);

    Route::get('/competition/{lang?}', [
        'uses' => 'CompetitionController@sevenBars',
        'as' => 'website.competition.7bars'
    ]);

    Route::post('/Jumeirah-Beach/register', [
        'uses' => 'CompetitionController@register',
        'as' => 'website.competition.register'
    ]);

    Route::get('/timer', [
        'uses' => 'CompetitionController@timer',
        'as' => 'website.competition.timer'
    ]);


    Route::get('/competition/participants/123asd', [
        'uses' => 'CompetitionController@participants'
    ]);

    // News Routes

    Route::get('/news/news-feed', [
        'uses' => 'NewsController@newsFeed'
    ]);

    Route::get('/news/{id}/{title?}', [
        'uses' => 'NewsController@newsItem',
        'as' => 'website.news.news_item'
    ]);

    Route::get('/contact-us', [
        'uses' => 'ContactUsController@showContactUsPage',
        'as' => 'website.contact_us.show_contact_us_page'
    ]);

    // vi homepage routes

    Route::get('/vi-home/{lang?}', [
        'uses' => 'ViHomePageController@index',
        'as' => 'website.vi_home_page.index'
    ]);

    Route::get('/vi-home2/{lang?}', [
        'uses' => 'ViHomePageController@index2',
        'as' => 'website.vi_home_page.index2'
    ]);

    Route::post('/contact-us', [
        'uses' => 'ViHomePageController@postContactUsForm',
        'as' => 'website.contact_us.post_contact_us_form'
    ]);

    Route::get('/vi-home/contact-us-messages/asdfgh/{form_id?}', [
        'uses' => 'ViHomePageController@contactUsMessages',
        'as' => 'website.vi_home_page.contact_us_messages'
    ]);

    Route::get('/stocks', [
        'uses' => 'HomeController@stocks'
    ]);

    Route::get('/stocks-users', [
        'uses' => 'HomeController@stocksUsers'
    ]);

    Route::get('/kafo-program/{lang?}', [
        'uses' => 'KafoController@index',
        'as' => 'website.kafo.index'
    ]);

    Route::post('/kafo-register', [
        'uses' => 'KafoController@register',
        'as' => 'website.kafo.register'
    ]);

    // For Contact

    Route::get('/for-contact/{name}', [
        'uses' => 'ForContactController@index',
        'as' => 'website.for_contact.index'
    ]);

    Route::get('/for-contact/{name}/participants', [
        'uses' => 'ForContactController@participants',
        'as' => 'website.for_contact.participants'
    ]);

});


Route::get('/admin', [
    'uses' => 'Admin\AuthController@getLogin',
    'as' => 'admin.get.login',
    'middleware' => 'guest'
]);

Route::post('/admin', [
    'uses' => 'Admin\AuthController@postLogin',
    'as' => 'admin.post.login',
    'middleware' => 'guest'
]);

Route::group(['prefix' => 'admin', 'namespace' => 'Admin', 'middleware' => 'auth'], function () {


    Route::get('/seminars/{seminar_id?}', [
        'uses' => 'SeminarController@seminarsWithParticipants',
        'as' => 'seminars'
    ]);

    Route::get('/create-seminar', [
        'uses' => 'SeminarController@createSeminar',
        'as' => 'seminars.create',
        'middleware' => 'admin.super'
    ]);

    Route::post('/save-seminar', [
        'uses' => 'SeminarController@saveSeminar',
        'as' => 'seminars.save',
        'middleware' => 'admin.super'
    ]);

    Route::get('/edit-seminar/{seminar_id}', [
        'uses' => 'SeminarController@editSeminar',
        'as' => 'seminars.edit',
        'middleware' => 'admin.super'
    ]);

    Route::post('/update-seminar/{seminar_id}', [
        'uses' => 'SeminarController@updateSeminar',
        'as' => 'seminars.update',
        'middleware' => 'admin.super'
    ]);

    Route::get('/delete-seminar/{seminar_id}', [
        'uses' => 'SeminarController@deleteSeminar',
        'as' => 'seminars.delete',
        'middleware' => 'admin.super'
    ]);

    Route::get('/delete-lecturer-image/{lecturer_id}', [
        'uses' => 'SeminarController@deleteLecturerImage',
        'as' => 'delete.lecturer.image',
        'middleware' => 'admin.super'
    ]);

    Route::get('/delete-lecturer/{lecturer_id}', [
        'uses' => 'SeminarController@deleteLecturer',
        'as' => 'delete_lecturer',
        'middleware' => 'admin.super'
    ]);

//    Route::get('excel/export/{model}/{seminar_id}' , [
//        'uses' => 'ExcelController@export' ,
//        'as' => 'excel.export'
//    ]);

    Route::get('create-user', [
        'uses' => 'AuthController@createUser',
        'as' => 'user.create',
        'middleware' => 'admin.super'
    ]);

    Route::post('save-user', [
        'uses' => 'AuthController@saveUser',
        'as' => 'user.save',
        'middleware' => 'admin.super'
    ]);

    Route::get('delete-user/{id}', [
        'uses' => 'AuthController@deleteUser',
        'as' => 'auth.delete_user',
        'middleware' => 'admin.super'
    ]);

    Route::get('logout', [
        'uses' => 'AuthController@logout',
        'as' => 'logout'
    ]);

});

// live trading competition
Route::get('/live-trading-competition/participants', [
    'uses' => 'LiveTradingCompetitionController@participants',
    'as' => 'live_trading_competition.participants',
    'middleware' => 'auth'
]);

Route::get('/live-trading-competition2/terms-and-conditions', [
    'uses' => 'LiveTradingCompetitionController@termsAndConditions',
    'as' => 'live_trading_competition.terms_and_conditions'
]);

Route::get('/live-trading-competition3/terms-and-condition3', [
    'uses' => 'LiveTradingCompetitionController@termsAndConditions3',
    'as' => 'live_trading_competition.terms_and_conditions3'
]);

Route::get('/live-trading-competition3/{lang?}', [
    'uses' => 'LiveTradingCompetitionController@index3',
    'as' => 'live_trading_competition.index3'
]);

Route::get('/live-trading-competition2/{lang?}', [
    'uses' => 'LiveTradingCompetitionController@index2',
    'as' => 'live_trading_competition.index2'
]);

Route::get('/live-trading-competition/{lang?}', [
    'uses' => 'LiveTradingCompetitionController@index',
    'as' => 'live_trading_competition.index'
]);

Route::post('/live-trading-competition/join', [
    'uses' => 'LiveTradingCompetitionController@join',
    'as' => 'live_trading_competition.join'
]);

// employee customer register
Route::get('/employee-register/{by?}', [
    'uses' => 'Website\EmployeeClientRegisterController@employee',
    'as' => 'employee_client_register.employee',
]);

Route::get('/client-register/{by?}', [
    'uses' => 'Website\EmployeeClientRegisterController@client',
    'as' => 'employee_client_register.client',
]);

//Route::get('/vimarkets/registrations/{type}/{by?}' , [
//    'uses' => 'Website\EmployeeClientRegisterController@data' ,
//    'as' => 'employee_client_register.data',
//    'middleware' => 'auth'
//]);
//
//Route::get('/vimarkets/registrations/{id}/download/{file}' , [
//    'uses' => 'Website\EmployeeClientRegisterController@download' ,
//    'as' => 'employee_client_register.download',
//    'middleware' => 'auth'
//]);

Route::post('/employee-client/register', [
    'uses' => 'Website\EmployeeClientRegisterController@register',
    'as' => 'employee_client_register.register',
]);


// artisan commands

Route::get('/artisan/migrate', function () {

    \Illuminate\Support\Facades\Artisan::call('migrate');

});

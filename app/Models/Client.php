<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Client extends Model
{

    const REGISTER_TYPE_CLIENT = 'client';
    const REGISTER_TYPE_EMPLOYEE = 'employee';

    protected $fillable = [
        'first_name',
        'last_name',
        'phone_number',
        'email',
        'work_place',
        'job_title',
        'section',
        'deposit_amount',
        'years_of_work',
        'civil_id',
        'passport',
        'address',
        'full_address',
        'proof_of_address',
        'notes',
        'opened_by',
        'marketed_by',
        'register_type',
        'by',
        'account_type',
        'stocks_type',
        'axi_select_program',
        'platforms',
    ];

    public static $files = [
        'passport',
        'proof_of_address',
        'civil_id',
    ];

    public function fullName () {

        return $this->first_name . ' ' . $this->last_name;

    }

    public function uploadFile ($file, $field) {

        if ($file) {

            $path = $file->store(
                'clients',
                'public_storage'
            );

            $this->{$field} = $this->{$field} ? ($this->{$field} . 'separator' . $path) : $path;

            return config('filesystems.disks.public_storage.root') . '/' . $path;

        }

        return null;

    }

    public function fileUrl ($field) {

        if (!$this->{$field}) {
            return null;
        }

        return Storage::disk('public_storage')->url($this->{$field});

    }

}

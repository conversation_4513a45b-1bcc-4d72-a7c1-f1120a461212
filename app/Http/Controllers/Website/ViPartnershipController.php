<?php

namespace App\Http\Controllers\Website;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ViPartnershipController
{
    public function index()
    {
        return view('website.vi-partnership');
    }

    public function send(Request $request)
    {
        $data = $request->all();

        unset($data['_token']);

        // send email
        Mail::send('emails.partnership', ['items' => $data], function ($m) {
            $m->from(config('mail.username'), 'Accounts');
            $m->to('<EMAIL>', 'Partners vimarkets');
            $m->subject('New Partnership Registration ' . date('Y-m-d H:i:s'));
        });

        return redirect()->back()->with('success', 'تم ارسال التسجيل بنجاح');
    }
}
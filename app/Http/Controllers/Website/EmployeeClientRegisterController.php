<?php

namespace App\Http\Controllers\Website;

use App\Models\Client;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class EmployeeClientRegisterController extends Controller
{

    public $users = [
        ["email" => "<EMAIL>", "name" => "hawraa-almousawi"],
        ["email" => "<EMAIL>", "name" => "zahraa-almousawi"],
        ["email" => "<EMAIL>", "name" => "narjs-almousawi"],
        ["email" => "<EMAIL>", "name" => "bishayer-faraj"],
        ["email" => "<EMAIL>", "name" => "zeinab-nassar"],
        ["email" => "<EMAIL>", "name" => "najd-al<PERSON><PERSON><PERSON>"],
        ["email" => "<EMAIL>", "name" => "shooq-halawa"],
        ["email" => "<EMAIL>", "name" => "anwar-aldhafeeri"],
        ["email" => "<EMAIL>", "name" => "ahad-ghader"],
        ["email" => "<EMAIL>", "name" => "zahraa-altamemi"],
        ["email" => "<EMAIL>", "name" => "hiba-teshouri"],
        ["email" => "<EMAIL>", "name" => "khaled-kheir"],
        ["email" => "<EMAIL>", "name" => "aber-aldousari"],
        ["email" => "<EMAIL>", "name" => "shaikhah-alnassar"],
        ["email" => "<EMAIL>", "name" => "omar-bawazir"],
        ["email" => "<EMAIL>", "name" => "fatma-alholi"],
        ["email" => "<EMAIL>", "name" => "ali-alkhalaf"],
        ["email" => "<EMAIL>", "name" => "thunayan-ali"],
        ["email" => "<EMAIL>", "name" => "noor-alidani"],
        ["email" => "<EMAIL>", "name" => "sara-mahmood"],
        ["email" => "<EMAIL>", "name" => "tariq-aqrabawi"],
        ["email" => "<EMAIL>", "name" => "frederik-nassif"],
        ["email" => "<EMAIL>", "name" => "amer-alchriti"],
        ["email" => "<EMAIL>", "name" => "mussa-naaman"], 
        ["email" => "<EMAIL>", "name" => "t-aljabawi"],
        ["email" => "<EMAIL>", "name" => "bashar"],
    ];
    
    public function employee ($by = null) {

        if ($by == null) {
            return redirect()->route('employee_client_register.employee', 'client-service');
        }

        if (!in_array($by, [
            'client-service',
            'mr-talal-department',
            'oman',
            'kafo-program',
            'vi-care',
            'lets-trade',
            'climaxkw',
             'vi-axi',
        ])
        ) {
            abort(404);
        }
        
        $isOfm = strpos(\request()->fullUrl(), 'ofm-kuwait') != false;


        return view('client_register', [
            'register_type' => Client::REGISTER_TYPE_EMPLOYEE,
            'by' => $by,
            'isOfm' => $isOfm
        ]);

    }

    public function client ($by = null) {

        if ($by == null) {
            return redirect()->route('employee_client_register.client', 'client-service');
        }
        
        if (!in_array($by, array_merge(
            [
                'client-service',
                'mr-talal-department',
                'kafo-program',
                'vi-care',
                'lets-trade',
                'climaxkw',
            ],
            array_column($this->users, 'name')
        ))) {
            abort(404);
        }
        
        $isOfm = strpos(\request()->fullUrl(), 'ofm-kuwait') != false;

        return view('client_register', [
            'register_type' => Client::REGISTER_TYPE_CLIENT,
            'by' => $by,
            'isOfm' => $isOfm
        ]);

    }

    public function register (Request $request) {

        $lang = $request->input('lang');

        $by = (string) $request->input('by');

        $registerType = $request->input('register_type');

        app()->setLocale($lang);
        
        $title = strpos(\request()->fullUrl(), 'ofm-kuwait') != false ? 'OFM Kuwait' : 'AXI VI MENA';

        $this->validate($request, [
            'first_name' => 'required|max:250',
            'last_name' => 'required|max:250',
            'phone_number' => 'required|max:250',
            'email' => 'required|max:250',
            'work_place' => 'required|max:250',
            'job_title' => 'required|max:250',
            'section' => 'required|max:250',
            'years_of_work' => 'required|max:250',
            'civil_id' => 'required',
            'passport' => 'required',
            'account_type' => 'required',
        ]);

        $files = ['civil_id', 'passport', 'proof_of_address'];

        try{

            $client = new Client();

            $client->fill($request->except($files));

            $paths = [];

            foreach ($files as $file) {

                $uploadedFile = $request->file($file);

                if (is_array($uploadedFile)) {

                    foreach ($uploadedFile as $singleUploadedFile) {

                        if ($path = $client->uploadFile($singleUploadedFile, $file)) {

                            $paths[] = $path;

                        }

                    }

                }else{

                    if ($path = $client->uploadFile($uploadedFile, $file)) {

                        $paths[] = $path;

                    }

                }

            }

            $client->save();

            $emailTo = '<EMAIL>';

            $cc = '';

            $subject = '';


            foreach ($this->users as $user) {
                if ($by == $user['name'] && $registerType == Client::REGISTER_TYPE_CLIENT) {
                    $emailTo = $user['email'];

                    $subject = 'Vi Markets (client service)';

                    break;
                }
            }
            
            
            $isOfm = strpos(\request()->fullUrl(), 'ofm-kuwait') != false;
            

            if ($by == 'client-service' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = $title . ' (client service)';

            }

            if ($by == 'client-service' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>','<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = $title . ' (client service)';

            }

            if ($by == 'mr-talal-department' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = $title .' (Mr.talal department)';

            }

            if ($by == 'mr-talal-department' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = $title . ' (Mr.talal department)';

            }

            if ($by == 'eman-alyyaf-department' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = $title . ' (Eman Alayyaf department)';

            }

            if ($by == 'eman-alyyaf-department' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>',];

                $subject = $title . ' (Eman Alayyaf department)';

            }


            if ($by == 'kafo-program' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = 'Kafo program';

            }

            if ($by == 'kafo-program' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>',];

                $subject = 'Kafo program';

            }
            if ($by == 'oman' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = 'Kafo program';

            }

            if ($by == 'oman' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>',];

                $subject = 'Kafo program';

            }

            if ($by == 'vi-care' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = 'AXI VI MENA (Follow up)';

            }

            if ($by == 'vi-care' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = $title . ' (Follow up)';

            }

            if ($by == 'lets-trade' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = $title . ' (Oman Office)';

            }

            if ($by == 'lets-trade' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = $title . ' (Oman Office)';

            }
            
            if ($by == 'climaxkw' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = 'Climaxkw (client)';

            }

            if ($by == 'climaxkw' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',];

                $subject = 'Climaxkw (employee)';

            }
                        
            if ($by == 'vi-axi' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = $isOfm ? 'OFM (employee)' : 'VI AXI (employee)';

            }

            $subject .= ' ' . $client->first_name . ' ' . $client->last_name;

            $job = new \App\Jobs\SendClientRegisterEmail(
                $client,
                $registerType == Client::REGISTER_TYPE_CLIENT ?
                    $emailTo : '<EMAIL>',
//                '<EMAIL>',
                $paths,
                $cc,
                $subject
            );

            dispatch($job);

            return redirect()->back()->with('success', $lang == 'ar' ? 'شكرا لك' : 'Thank you');

        }catch (\Exception $exception) {

            Log::error($exception->getMessage());

            return redirect()->back()->with('failed', 'Something went wrong!');

        }

    }

    public function data ($register_type, $by = null) {

        $registrations = Client::query()->where('register_type', $register_type)->latest();

        if ($register_type == 'client' && $by != null) {
            $registrations = $registrations->where('by', $by);
        }elseif ($register_type == 'client' && $by == null) {
            $registrations = $registrations->where('by', '0');
        }

        $registrations = $registrations->paginate(30);

        return view('client_register_data', compact('registrations', 'register_type'));

    }

    public function download ($id, $path) {

//        $registration = Client::findOrFail($id);

//        $filePath = $registration->{$file};

        $path = decrypt($path);

        if (Storage::disk('public_storage')->exists($path)) {

            return Storage::disk('public_storage')->download(urldecode($path));

        }

        return redirect()->back();


    }

}

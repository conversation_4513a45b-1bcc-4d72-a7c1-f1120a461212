<?php

namespace App\Http\Controllers\Website;

use App\Models\TradeSharesUser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class TradeSharesController extends Controller
{

    public function index () {

        return view('website.trade_shares.index');

    }

    public function register (Request $request) {

        $this->validate($request, [
            'name' => ['required', 'max:250'],
            'phone' => ['required', 'max:250'],
        ]);

        TradeSharesUser::create($request->all());

        return redirect()
            ->back()
            ->with('success', 'شكرا لك لتسجيلك معنا');

    }

    public function users (Request $request, $type = TradeSharesUser::TYPE_ACCOUNT) {

        $users = TradeSharesUser::query()->where('type', $type)->latest()->paginate(25);

        return view('website.trade_shares.users', compact('users'));

    }

}

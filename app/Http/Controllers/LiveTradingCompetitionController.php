<?php

namespace App\Http\Controllers;

use App\Models\LiveTradingCompetition;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class LiveTradingCompetitionController extends Controller
{

    public function index () {

        return view('live_trading_competition');

    }

    public function index2 () {

        return view('live_trading_competition2');

    }

    public function index3 () {

        return view('live_trading_competition3');

    }

    public function termsAndConditions () {

        return response()
            ->make(Storage::disk('public_storage')->get('vi competition terms & conditions.pdf'))
            ->withHeaders([
                'Content-Type' => 'application/pdf'
            ]);

    }

    public function termsAndConditions3 () {

        return response()
            ->make(Storage::disk('public_storage')->get('Terms and Conditions Apply (2).pdf'))
            ->withHeaders([
                'Content-Type' => 'application/pdf'
            ]);

    }

    public function join (Request $request) {

        $this->validate($request, [
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'required|email',
            'phone' => 'required',
            'mt4_number' => 'required'
        ]);

        LiveTradingCompetition::create($request->all());

        return redirect()->back()
            ->with('success', 'تم الأشتراك بنجاح');

    }

    public function participants () {

        $participants = LiveTradingCompetition::whereDate('created_at', '>=', Carbon::parse('2020-6-1'))->get();

        return view('live_trading_competition_participants', compact('participants'));

    }

}

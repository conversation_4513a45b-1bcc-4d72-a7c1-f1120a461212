<?php

namespace App\Http\Controllers\Admin;

use App\Models\Participant;
use Illuminate\Http\Request;
use App\Models\User;
use App\Http\Requests;
use App\Http\Controllers\Controller;

class AuthController extends Controller
{

    protected $user;

    public function __construct (User $user)
    {
        $this->user = $user;
    }

    public function getLogin ()
    {

        return view('admin.login');

    }

    public function postLogin (Request $request)
    {

        if (auth()->attempt([
            'email' => $request->input('email') ,
            'password' => $request->input('password') ,
            'admin' => true
        ] , true)) {

            return redirect()->route('seminars');

        }else{

            return redirect()->back()->with('failed' , 'من فضلك تأكد من البيانات المدخلة');

        }

    }

    public function createUser () {

        $users = $this->user->get();

        return view('admin.create_user' , compact('users'));

    }

    public function saveUser (Request $request) {


        $this->validate($request , [
            'name' => 'required' ,
            'email' => 'required|email|unique:users' ,
            'password' => 'required' ,
            'password_confirmation' => 'required|same:password'
        ]);

        $this->user->create([
            'name' => $request->input('name'),
            'email' => $request->input('email') ,
            'password' => bcrypt($request->input('password')) ,
            'role' => (integer) $request->input('role') ?: 0,
            'admin' => true
        ]);

        return redirect()->back()->with('success' , 'تم أضافة الأدمن بنجاح');

    }

    public function deleteUser ($id) {

        $this->user->findOrFail($id)->delete();

        return redirect()->back();

    }

    public function logout () {

        auth()->logout();

        return redirect()->route('admin.get.login');

    }


}

<?php

    function translate ($text) {

        return $text[request()->get('lang') ?? 'en'];

    }
    
    $color = '#e30631';
    
    $accountTypes = [
        'Standard account',
        'Pro account',
        'Swap free account',
    ];

    $platforms = [
        'MT4',
        'MT5',
        'MT4 + MT5',
    ];

/**
 * المنصات

- MT4 (ميتاتريدر ٤)
- ⁠MT5 (ميتاتريدر ٥)
- ⁠MT4 + MT5 (ميتاتريدر ٤ و ميتاتريدر ٥)
 */

    $platformsTrans = [
        'MT4' => [
            'en' => 'MT4',
            'ar' => 'ميتاتريدر ٤',
        ],
        'MT5' => [
            'en' => 'MT5',
            'ar' => 'ميتاتريدر ٥',
        ],
        'MT4 + MT5' => [
            'en' => 'MT4 + MT5',
            'ar' => 'ميتاتريدر ٤ و ميتاتريدر ٥',
        ],
    ];
    
    if ($isOfm) {
        $accountTypes = [
            'Standard account',
            'Stock account',
        ];
    }

?>

<!doctype html>
<html lang="en" dir="{{ translate(['en' => 'ltr', 'ar' => 'rtl']) }}">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">

    <title>
        @if(\Illuminate\Support\Str::contains(request()->fullUrl(), 'axi-vi-mena.com'))
            AXI VI MENA
        @else
            OFM Kuwait
        @endif
        
         {{ $register_type == \App\Models\Client::REGISTER_TYPE_CLIENT ? 'Client' : 'Employee' }} Register</title>

    <style>


        #container {
            background-image: url("{{ asset('storage/background 4.png') }}");
            background-size: auto;
            height: 100vh;
            overflow: scroll;
        }

        #overlay {
            height: 100vh;
            overflow: scroll;
            /*background-color: rgba(0,0,0,0.4);*/
        }

        body {
            color: black;
            text-align: {{ translate([
                'en' => 'left',
                'ar' => 'right',
            ]) }};
        }

        .form-control {
            background-color: transparent;
            color: black;
            border: 1px solid #6c757d;
        }

        [v-cloak] {
            display: none;
        }

        .purple {
            color: #b700b7;
        }

        .background-gold {
            background-color: {{ $color }};
        }

        .border-gold {
            border-color: {{ $color }};
        }

        .gold {
            color: {{ $color }};
        }
        
        .btn-primary:hover {
            color: #fff;
            background-color: {{ $color }};
            border-color: {{ $color }};
        }

    </style>

</head>

    <body>

    <div id="container">

        <div id="overlay">

            <div id="app" class="container px-5">

                <div class="row justify-content-center justify-content-sm-start mt-3">

                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle background-gold" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            {{
                                translate([
                                    'en' => 'Change Language',
                                    'ar' => 'تغيير اللغة'
                                ])
                            }}
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['lang' => 'en']) }}">English</a>
                            <a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['lang' => 'ar']) }}">العربية</a>
                        </div>
                    </div>

                </div>

                <div class="d-flex flex-column align-items-center justify-content-center py-3">
                    
                    @if($isOfm)
                                      <div class="col-sm-6 text-center">
                        <img class="w-100" src="{{ asset('imgs/logos/one_financial_black.png') }}">
                    </div>

                    <div class="col-sm-6 text-center my-3">
                        <img class="w-75" src="{{ asset('imgs/logos/vi_markets.png') }}">
                    </div>
                    
                    @else
                                        <div class="col-sm-6 text-center my-2">
                        <img class="w-75" src="{{ asset('imgs/logos/vi-red.png') }}">
                    </div>
                    
                     <div class="col-sm-6 text-center my-2">
                        <img style="width: 130px" src="{{ asset('imgs/logos/axi_logo.png') }}">
                    </div>
                    @endif
                    

                    @if($by === 'climaxkw')
                    
                        <div class="col-sm-6 text-center mb-4">
                            <img class="w-50" src="{{ asset('imgs/climax-logo.png') }}">
                        </div>
                    
                    @endif
                    
                </div>

                <div class="row justify-content-center mb-4">

                    <div class="col-sm-12">

{{--                        <h2>--}}
{{--                            {{ translate([--}}
{{--                                'en' => 'Dear client',--}}
{{--                                'ar' => 'عميلنا العزيز'--}}
{{--                            ]) }}--}}
{{--                        </h2>--}}

                        <h3>
                            {{ translate([
                                'en' => 'Apply now to open a real account with ' . ($by === 'climaxkw' ? 'Climax' : 'VI Markets'),
                                'ar' => '  لفتح حساب جديد يرجى تزويدنا بالبيانات التالية',
                            ]) }}
                        </h3>

                    </div>

                </div>

                <div class="row justify-content-center">

                    <div class="col-sm-12">

                        @include('partials.success')
                        @include('partials.failed')
                        @include('partials.validation_errors')

                        <form action="{{ route('employee_client_register.register') }}" method="post" enctype="multipart/form-data">

                            {{ csrf_field() }}

                            <input type="hidden" name="register_type" value="{{ $register_type }}">

                            <input type="hidden" name="lang" value="{{ request()->get('lang') ? request()->get('lang') : 'en' }}">

                            @if(isset($by))
                                <input type="hidden" name="by" value="{{ $by }}">
                            @endif

                            <div class="form-group">
                                <label for="first_name">{{ translate([
                        'en' => 'First Name ',
                        'ar' => ' الأسم الأول',
                    ]) }}</label>
                                <input name="first_name" type="text" class="form-control" value="{{ old('first_name') }}" required>
                            </div>

                            <div class="form-group">
                                <label for="last_name">{{ translate([
                        'en' => 'Last Name ',
                        'ar' => ' الأسم الأخير',
                    ]) }}</label>
                                <input name="last_name" type="text" class="form-control" value="{{ old('last_name') }}" required>
                            </div>

                            <div class="form-group">
                                <label for="phone_number">{{ translate([
                        'en' => 'Phone Number ',
                        'ar' => ' رقم الهاتف',
                    ]) }}</label>
                                <input name="phone_number" type="text" class="form-control" value="{{ old('phone_number') }}" required>
                            </div>

                            <div class="form-group">
                                <label for="email">{{ translate([
                        'en' => 'Email ',
                        'ar' => ' البريد الالكتروني',
                    ]) }}</label>
                                <input name="email" type="text" class="form-control" value="{{ old('email') }}" required>
                            </div>

                            <div class="form-group">
                                <label>{{ translate([
                        'en' => 'Work Place ',
                        'ar' => ' جهة العمل',
                    ]) }}</label> /
                                <label class="gold">{{ translate([
                        'en' => 'University name',
                        'ar' => 'إسم الجامعة',
                    ]) }}</label>
                                <input name="work_place" type="text" class="form-control" value="{{ old('work_place')}}" required>
                            </div>

                            <div class="form-group">
                                <label for="job_title">{{ translate([
                        'en' => 'Job Title ',
                        'ar' => ' المسمى الوظيفي',
                    ]) }}</label> /
                                <label class="gold">{{ translate([
                        'en' => 'major',
                        'ar' => 'اسم التخصص ',
                    ]) }}</label>
                                <input name="job_title" type="text" class="form-control" value="{{ old('job_title')}}" required>
                            </div>

                            <div class="form-group">
                                <label>{{ translate([
                        'en' => 'Work sector ',
                        'ar' => ' القطاع',
                    ]) }}</label> /
                                <label class="gold">{{ translate([
                        'en' => 'university sector ',
                        'ar' => ' القطاع الجامعي',
                    ]) }}</label>
                                <select class="form-control" name="section" id="section">
                                    <option @if(old('section') == 'governmental') selected @endif value="governmental">
                                        {{ translate([
                                'en' => 'governmental',
                                'ar' => 'حكومي',
                            ]) }}
                                    </option>
                                    <option @if(old('section') == 'private') selected @endif value="private">
                                        {{ translate([
                                'en' => 'private',
                                'ar' => 'خاص',
                            ]) }}
                                    </option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>{{ translate([
                                    'en' => 'Account type',
                                    'ar' => 'نوع الحساب',
                                ]) }}</label>
                                <select v-model="account_type" class="form-control" name="account_type" id="account_type">
                                    @foreach($accountTypes as $accountType)
                                        <option @if(old('account_type') == $accountType) selected @endif value="{{ $accountType }}">
                                            {{ translate([
                                                'en' => $accountType,
                                                'ar' => $accountType,
                                            ]) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="form-group">
                                <label>{{ translate([
                                    'en' => 'Platforms',
                                    'ar' => 'المنصات',
                                ]) }}</label>
                                <select v-model="platforms" class="form-control" name="platforms" id="platforms">
                                    @foreach($platforms as $platform)
                                        <option @if(old('platforms') == $platform) selected @endif value="{{ $platform }}">
                                            {{ translate([
                                                'en' => $platformsTrans[$platform]['en'],
                                                'ar' => $platformsTrans[$platform]['ar'],
                                            ]) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            @if(!$isOfm)
                                <div class="form-group">
                                    <label>{{ translate([
                                        'en' => 'Axi Select Program',
                                        'ar' => 'Axi Select Program',
                                    ]) }}</label>
                                    <select v-model="axi_select_program" class="form-control" name="axi_select_program" id="axi_select_program">
                                        @foreach(['yes', 'no'] as $option)
                                            <option @if(old('axi_select_program') == $option) selected @endif value="{{ $option }}">
                                                {{ translate([
                                                    'en' => $option,
                                                    'ar' => $option,
                                                ]) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            @endif

                            <div class="form-group">
                                <label>{{ translate([
                        'en' => 'Years of work ',
                        'ar' => ' عدد سنوات العمل',
                    ]) }}</label> /
                                <label class="gold">{{ translate([
                        'en' => 'years of study',
                        'ar' => ' عدد سنوات الدراسة',
                    ]) }}</label>
                                <input name="years_of_work" type="number" class="form-control" value="{{ old('years_of_work') }}" required>
                            </div>

                            <div class="form-group">
                                <label for="full_address">
                                    {{ translate([
                                'en' => 'Full address',
                                'ar' => 'العنوان/السكن الكامل',
                            ]) }}
                                </label>
                                <textarea id="full_address" name="full_address" class="form-control">{{ old('full_address') }}</textarea>
                            </div>

                            <div class="form-group">
                                <h3 class="gold">
                                    {{ translate([
                                'en' => 'Please note: University data needed if the applier is a student, and please provide us with details regarding work place and job title of one of your parents. ',
                                'ar' => 'تنويه : البيانات الجامعية مطلوبة فيما لو كان صاحب الطلب طالب جامعي , و نرجو منكم تزويدنا في الملاحظات بالمسمى الوظيفي و مقر العمل لأحد الوالدين .',
                            ]) }}

                                </h3>
                            </div>

                            <div class="form-group">
                                <label for="work_place">{{ translate([
                        'en' => 'Deposit amount',
                        'ar' => 'المبلغ المراد إيداعه',
                    ]) }}</label>
                                <input name="deposit_amount" type="text" class="form-control" value="{{ old('deposit_amount') }}">
                            </div>

                            @if($register_type == \App\Models\Client::REGISTER_TYPE_EMPLOYEE)

                                <div class="form-group">
                                    <label for="opened_by">{{ translate([
                        'en' => 'The account is opened by (name of the employee)',
                        'ar' => 'تم فتح الحساب من قبل (اسم الموظف)',
                    ]) }}</label>
                                    <input name="opened_by" type="text" class="form-control" value="{{ old('opened_by') }}" required>
                                </div>

                                <div class="form-group">
                                    <label for="marketed_by">{{ translate([
                        'en' => 'Explained and marketed by (name of the employee)',
                        'ar' => ' تم الشرح و الإقناع من قبل (اسم الموظف)',
                    ]) }}</label>
                                    <input name="marketed_by" type="text" class="form-control" value="{{ old('marketed_by') }}" required>
                                </div>

                            @endif

                            <div class="form-group">
                                <h3>
                                    {{ translate([
                                'en' => 'Documents needed to register an account',
                                'ar' => ' المستندات المطلوبة لفتح الحساب',
                            ]) }}

                                </h3>
                            </div>


                            <div class="row">

                                <div @click="selected = 'kuwait'" class="form-group col-lg-4">
                                        <button class="btn btn-primary btn-block background-gold border-gold" type="button">
                                            {{ translate([
                                    'en' => '* In Kuwait ',
                                    'ar' => '  * في الكويت',
                                ]) }}
                                        </button>
                                </div>

                                <div @click="selected = 'gulf'" class="form-group col-lg-4">
                                        <button class="btn btn-primary btn-block background-gold border-gold" type="button">
                                            {{ translate([
                                    'en' => '* In gulf countries',
                                    'ar' => '* دول الخليج  ',
                                ]) }}
                                        </button>

                                </div>

                                <div @click="selected = 'out'" class="form-group col-lg-4">
                                        <button class="btn btn-primary btn-block background-gold border-gold" type="button">
                                            {{ translate([
                                    'en' => '* Other countries',
                                    'ar' => '* خارج الكويت ودول الخليج',
                                ]) }}
                                        </button>
                                </div>

                            </div>

                            <div v-if="selected == 'kuwait'">

                                <div class="form-group">
                                    <label for="civil_id">{{ translate([
                        'en' => 'Civil ID scan front and back',
                        'ar' => '  بطاقة مدنية من الامام والخلف',
                    ]) }}</label>
                                    <input name="civil_id[]" type="file" class="form-control" multiple max="2" >
                                </div>

                                <div class="form-group">
                                    <label for="passport">{{ translate([
                        'en' => 'Driving license or passport scan front and back',
                        'ar' => ' رخصة قيادة أو جواز سفر من الأمام والخلف',
                    ]) }}</label>
                                    <input name="passport[]" type="file" class="form-control" multiple max="2" >
                                </div>

                            </div>

                            <div v-if="selected == 'gulf'">

                                <div class="form-group">
                                    <label for="civil_id">{{ translate([
                        'en' => 'Civil ID scan front and back ',
                        'ar' => ' بطاقة مدنية من الامام والخلف',
                    ]) }}</label>
                                    <input name="civil_id[]" type="file" class="form-control" multiple max="2" >
                                </div>

                                <div class="form-group">
                                    <label for="passport">{{ translate([
                        'en' => 'Driving license or passport  ',
                        'ar' => ' رخصة قيادة أو جواز سفر',
                    ]) }}</label>
                                    <input name="passport[]" type="file" class="form-control" multiple max="2" >
                                </div>

                                <div class="form-group">
                                    <label for="address">
                                        {{ translate([
                                    'en' => 'Address (Area, street, house number, floor) ',
                                    'ar' => 'عنوان السكن بشكل مفصل (المنطقة - الشارع - رقم المنزل – الدور)',
                                ]) }}
                                    </label>
                                    <textarea name="address" class="form-control">{{ old('address') }}</textarea>
                                </div>

                            </div>

                            <div v-if="selected == 'out'">

                                <div class="form-group">
                                    <label for="civil_id">{{ translate([
                        'en' => 'Civil ID scan front and back ',
                        'ar' => ' بطاقة مدنية من الامام والخلف',
                    ]) }}</label>
                                    <input name="civil_id[]" type="file" class="form-control" multiple max="2" >
                                </div>

                                <div class="form-group">
                                    <label for="passport">{{ translate([
                        'en' => 'Driving license or passport  ',
                        'ar' => 'رخصة قيادة أو جواز سفر  ',
                    ]) }}</label>
                                    <input name="passport[]" type="file" class="form-control" multiple max="2" >
                                </div>

                                <div class="form-group">
                                    <label for="proof_of_address">{{ translate([
                        'en' => 'Proof of address  ',
                        'ar' => 'إثبات سكن  ',
                    ]) }}</label>
                                    <input name="proof_of_address" type="file" class="form-control" >
                                </div>

                            </div>

                            <div class="form-group">
                                <label>
                                    {{ translate([
                                'en' => 'Notes',
                                'ar' => 'ملاحظات',
                            ]) }}
                                </label>
                                <label class="font-weight-lighter">
                                    ({{ translate([
                                'en' => 'How did you hear about us?',
                                'ar' => 'من أين عرفت عن الشركة؟',
                            ]) }})
                                </label>
                                <textarea name="notes" class="form-control">{{ old('notes') }}</textarea>
                            </div>

                            <div class="form-group">
                                <button class="btn btn-danger" type="submit">
                                    {{ translate([
                                        'en' => 'Submit',
                                        'ar' => 'أرسال'
                                    ])  }}
                                </button>
                            </div>

                        </form>

                    </div>

                </div>

            </div>


        </div>

    </div>

    <script src="{{ asset('js/jquery-3.4.1.slim.min.js') }}"></script>
    <script src="{{ asset('js/popper.min.js') }}"></script>
    <script src="{{ asset('js/bootstrap4.min.js') }}"></script>

    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>

    <script>

        new Vue({

            el: '#app',

            data : {
                selected: '',
                account_type: '{{ $accountTypes[0] }}',
                platforms: '{{ $platforms[0] }}',
                axi_select_program: 'yes'
            }

        })

    </script>

    </body>

</html>

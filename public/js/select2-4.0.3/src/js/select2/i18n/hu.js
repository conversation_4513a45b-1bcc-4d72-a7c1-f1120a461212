define(function () {
  // Hungarian
  return {
    inputTooLong: function (args) {
      var overChars = args.input.length - args.maximum;

      return 'T<PERSON> ho<PERSON> ' + over<PERSON>hars + ' karak<PERSON><PERSON> több, mint kellene.';
    },
    inputTooShort: function (args) {
      var remainingChars = args.minimum - args.input.length;

      return 'Túl rövid. Még ' + remainingChars + ' karakter hi<PERSON>.';
    },
    loadingMore: function () {
      return 'Töltés…';
    },
    maximumSelected: function (args) {
      return 'Csak ' + args.maximum + ' elemet lehet kiválas<PERSON>tani.';
    },
    noResults: function () {
      return 'Nincs találat.';
    },
    searching: function () {
      return 'Keresés…';
    }
  };
});

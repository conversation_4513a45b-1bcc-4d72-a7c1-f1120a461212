{"name": "select2", "title": "Select2", "description": "Select2 is a jQuery based replacement for select boxes. It supports searching, remote data sets, and infinite scrolling of results.", "keywords": ["select", "autocomplete", "typeahead", "dropdown", "multiselect", "tag", "tagging"], "version": "4.0.3", "author": {"name": "<PERSON>", "url": "https://github.com/kevin-brown"}, "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "bugs": "https://github.com/select2/select2/issues", "homepage": "https://select2.github.io", "docs": "https://select2.github.io", "download": "https://github.com/select2/select2/releases", "dependencies": {"jquery": ">=1.7.2"}}
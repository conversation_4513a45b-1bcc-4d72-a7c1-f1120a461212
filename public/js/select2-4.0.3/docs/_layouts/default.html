<!doctype html>
<html>
  <head>
    {% include head.html %}
  </head>
  <body>
    {% include navigation.html %}

    {{ content }}

    {% include footer.html %}

    <script>
      (function ($) {
        'use strict';

        $(function () {
          var $window = $(window);
          var $body = $(document.body);
          var $sidebar = $('.s2-docs-sidebar');

          $body.scrollspy({
            target: '.s2-docs-sidebar',
            offset: 40
          });

          $window.on('load', function () {
            $body.scrollspy('refresh');
          });

          $sidebar.affix({
            offset: {
              top: function () {
                var offsetTop = $sidebar.offset().top;
                var navOuterHeight = $('.s2-docs-nav').height();

                return (this.top = offsetTop - navOuterHeight);
              },
              bottom: function () {
                return (this.bottom = $('.s2-docs-footer').outerHeight(true));
              }
            }
          });
        });
      })(jQuery);

      (function () {
        'use strict';

        anchors.options.placement = 'left';
        anchors.add('.s2-docs-container h1, .s2-docs-container h2, .s2-docs-container h3, .s2-docs-container h4, .s2-docs-container h5');
      })();
    </script>

    {% include ga.html %}
  </body>
</html>

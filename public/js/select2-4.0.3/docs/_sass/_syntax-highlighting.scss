// Jekyll syntax highlighting styles adjusted to match Google Code Prettify

.highlight  {
    background: #fff;

    // Text
    .nx {
        color: #333;
    }

    // Keywords and operators
    .k, .o {
        font-weight: bold;
    }

    // Attribute name
    .na {
        color: #428BCA;
    }

    // Strings
    .s, .s1, .s2, .sb, .sc, .sd, .se, .sh, .si, .sx {
        color: #C7254E;
    }

    // Literals
    .m, .mf, mh, .mi, .mo {
        color: #195f91;
    }

    // Parentheses
    .p {
        color: #93a1a1;
    }

    // Tag
    .nt {
        color: #2F6F9F;
    }

    // Comments
    .c {
        color: #999;
        font-style: italic;
    }

    // Error
    .err {
        background-color: #e3d2d2;
        color: #a61717;
    }

    // Generic error
    .gr {
        color: #a00;
    }

    // Container styles
    pre {
        border: none;
        margin: 0;
    }

    & > pre {
        border: 1px solid #eee;
        padding: 0;
        margin-bottom: 14px;
    }

    // Line numbers
    .lineno {
        background-color: #fbfbfb;
        color: #bebebe;

        -ms-user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
    }
}

.highlight .cm { color: #999988; font-style: italic } /* Comment.Multiline */
.highlight .cp { color: #999999; font-weight: bold } /* Comment.Preproc */
.highlight .c1 { color: #999988; font-style: italic } /* Comment.Single */
.highlight .cs { color: #999999; font-weight: bold; font-style: italic } /* Comment.Special */
.highlight .gd { color: #000000; background-color: #ffdddd } /* Generic.Deleted */
.highlight .gd .x { color: #000000; background-color: #ffaaaa } /* Generic.Deleted.Specific */
.highlight .ge { font-style: italic } /* Generic.Emph */
.highlight .gh { color: #999999 } /* Generic.Heading */
.highlight .gi { color: #000000; background-color: #ddffdd } /* Generic.Inserted */
.highlight .gi .x { color: #000000; background-color: #aaffaa } /* Generic.Inserted.Specific */
.highlight .go { color: #888888 } /* Generic.Output */
.highlight .gp { color: #555555 } /* Generic.Prompt */
.highlight .gs { font-weight: bold } /* Generic.Strong */
.highlight .gu { color: #aaaaaa } /* Generic.Subheading */
.highlight .gt { color: #aa0000 } /* Generic.Traceback */
.highlight .kc { font-weight: bold } /* Keyword.Constant */
.highlight .kd { font-weight: bold } /* Keyword.Declaration */
.highlight .kp { font-weight: bold } /* Keyword.Pseudo */
.highlight .kr { font-weight: bold } /* Keyword.Reserved */
.highlight .kt { color: #445588; font-weight: bold } /* Keyword.Type */
.highlight .nb { color: #0086B3 } /* Name.Builtin */
.highlight .nc { color: #445588; font-weight: bold } /* Name.Class */
.highlight .no { color: #008080 } /* Name.Constant */
.highlight .ni { color: #800080 } /* Name.Entity */
.highlight .ne { color: #990000; font-weight: bold } /* Name.Exception */
.highlight .nf { color: #990000; font-weight: bold } /* Name.Function */
.highlight .nn { color: #555555 } /* Name.Namespace */
.highlight .nv { color: #008080 } /* Name.Variable */
.highlight .ow { font-weight: bold } /* Operator.Word */
.highlight .w { color: #bbbbbb } /* Text.Whitespace */
.highlight .sr { color: #009926 } /* Literal.String.Regex */
.highlight .ss { color: #990073 } /* Literal.String.Symbol */
.highlight .bp { color: #999999 } /* Name.Builtin.Pseudo */
.highlight .vc { color: #008080 } /* Name.Variable.Class */
.highlight .vg { color: #008080 } /* Name.Variable.Global */
.highlight .vi { color: #008080 } /* Name.Variable.Instance */
.highlight .il { color: #009999 } /* Literal.Number.Integer.Long */
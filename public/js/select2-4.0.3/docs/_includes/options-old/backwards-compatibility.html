<section>
  <div class="page-header">
    <h1 id="compatibility">Backwards compatibility</h1>
  </div>

  <p>
    Select2 offers limited backwards compatibility with the previously 3.5.x
    release line, allowing people more efficiently transfer across releases
    and get the latest features. For many of the larger changes, such as the
    change in how custom data adapters work, compatibility modules were
    created that will be used to assist in the upgrade process. It is not
    recommended to rely on these compatibility modules, as they will not
    always exist, but they make upgrading easier for major changes.
  </p>

  <p>
    <strong>The compatibility modules are only included in the
    <a href="index.html#builds-full" class="alert-link">full builds</a> of
    Select2</strong>. These files end in <code>.full.js</code>, and the
    compatibility modules are prefixed with <code>select2/compat</code>.
  </p>

  <h2 id="compat-matcher">
    Simplified function for matching data objects
  </h2>

  <p class="alert alert-info">
    <a href="announcements-4.0.html#new-matcher" class="alert-link">Added in Select2 4.0.0.</a>
    This method was added to make upgrading easier from earlier versions of
    Select2.
  </p>

  <p>
    During the <a href="announcements-4.0.html">Select2 4.0.0 release</a>, the
    <code>matcher</code> function was changed to allow for more complex
    matching of nested objects.
  </p>

  <div class="row">
    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Key</dt>
        <dd>
          <code>matcher</code>
        </dd>

        <dt>Value</dt>
        <dd>
          A function taking a search <code>term</code> and the data object
          <code>text</code>.
        </dd>
      </dl>
    </div>

    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Adapter</dt>
        <dd>
          <code title="select2/compat/matcher">oldMatcher</code>
        </dd>
      </dl>
    </div>
  </div>

  <p>
    The <a href="examples.html#matcher">custom matcher example</a> provides a
    guide for how to use this in your own application. For those upgrading
    from older versions of Select2, you just need to wrap your old
    <code>matcher</code> with this function to maintain compatibility.
  </p>

  <h2 id="initSelection">
    Old initial selections with <code>initSelection</code>
  </h2>

  <p class="alert alert-warning">
    <a href="announcements-4.0.html#removed-initselection" class="alert-link">Deprecated in Select2 4.0.</a>
    This has been replaced by another option and is only available in the
    <a href="index.html#builds-full" class="alert-link">full builds</a> of
    Select2.
  </p>

  <p>
    In the past, Select2 required an option called <code>initSelection</code>
    that was defined whenever a custom data source was being used, allowing
    for the initial selection for the component to be determined. This has
    been replaced by the <code>current</code> method on the
    <a href="#dataAdapter">data adapter</a>.
  </p>

  <div class="row">
    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Key</dt>
        <dd>
          <code>initSelection</code>
        </dd>

        <dt>Value</dt>
        <dd>
          A function taking a <code>callback</code>
        </dd>
      </dl>
    </div>

    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Adapter</dt>
        <dd>
          <code title="select2/data/base">DataAdapter</code>
        </dd>

        <dt>Decorator</dt>
        <dd>
          <code title="select2/compat/initSelection">InitSelection</code>
        </dd>
      </dl>
    </div>
  </div>

  <h2 id="query">
    Querying old data with <code>query</code>
  </h2>

  <p class="alert alert-warning">
    <a href="announcements-4.0.html#query-to-data-adapter" class="alert-link">Deprecated in Select2 4.0.</a>
    This has been replaced by another option and is only available in the
    <a href="index.html#builds-full" class="alert-link">full builds</a> of
    Select2.
  </p>

  <p>
    In the past, Select2 supported an option called <code>query</code> that
    allowed for a custom data source to be used. This option has been replaced
    by the <code>query</code> method on the
    <a href="#dataAdapter">data adapter</a> and takes a very similar set of
    parameters.
  </p>

  <div class="row">
    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Key</dt>
        <dd>
          <code>query</code>
        </dd>

        <dt>Value</dt>
        <dd>
          A function taking <code>params</code> (including a <code>callback</code>)
        </dd>
      </dl>
    </div>

    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Adapter</dt>
        <dd>
          <code title="select2/data/base">DataAdapter</code>
        </dd>

        <dt>Decorator</dt>
        <dd>
          <code title="select2/compat/query">Query</code>
        </dd>
      </dl>
    </div>
  </div>

  <h2 id="input-fallback">
    Compatibility with <code>&lt;input type="text" /&gt;</code>
  </h2>

  <p class="alert alert-warning">
    <a href="announcements-4.0.html#hidden-input" class="alert-link">Deprecated in Select2 4.0.</a>
    It is now encouraged to use the <code>&lt;select&gt;</code> tag instead.
  </p>

  <p>
    In past versions of Select2, a <code>&lt;select&gt;</code> element could
    only be used with a limited subset of options. An
    <code>&lt;input type="hidden" /&gt;</code> was required instead, which did
    not allow for a graceful fallback for users who did not have JavaScript
    enabled. Select2 now supports the <code>&lt;select&gt;</code> element for
    all options, so it is no longer required to use <code>&lt;input /&gt;</code>
    elements with Select2.
  </p>

  <dl class="dl-horizontal">
    <dt>Adapter</dt>
    <dd>
      <code title="select2/data/base">DataAdapter</code>
    </dd>

    <dt>Decorator</dt>
    <dd>
      <code title="select2/compat/inputData">InputData</code>
    </dd>
  </dl>
</section>

<nav class="s2-docs-sidebar hidden-print hidden-xs hidden-sm">
  <ul class="nav s2-docs-sidenav">
    <li>
      <a href="#core-options">Core Options</a>
      <ul class="nav">
        <li><a href="#data-attributes">Declaring configuration in the <code>data-*</code> attributes</a></li>
        <li><a href="#amd">AMD compatibility</a></li>
        <li><a href="#core-options-display">Displaying selections</a></li>
        <li><a href="#core-options-results">Returning and displaying results</a></li>
      </ul>
    </li>
    <li>
      <a href="#dropdown">Dropdown</a>
      <ul class="nav">
        <li><a href="#dropdownParent">Attached to body</a></li>
        <li><a href="#dropdown-attachContainer">Attached below the container</a></li>
        <li><a href="#dropdown-search">Search</a></li>
        <li><a href="#dropdown-select-on-close">Select the highlighted option on close</a></li>
        <li><a href="#closeOnSelect">Close the dropdown when a result is selected</a></li>
      </ul>
    </li>
    <li>
      <a href="#events">Events</a>
      <ul class="nav">
        <li><a href="#events-public">Public events</a></li>
        <li><a href="#events-internal">Internal events</a></li>
      </ul>
    </li>
    <li>
      <a href="#adapters">The plugin system (adapters)</a>
      <ul class="nav">
        <li><a href="#adapters-all">All adapters</a></li>
        <li><a href="#selectionAdapter">Container (selection)</a></li>
        <li><a href="#dataAdapter">Data set</a></li>
        <li><a href="#dropdownAdapter">Dropdown</a></li>
        <li><a href="#resultsAdapter">Results</a></li>
      </ul>
    </li>
    <li>
      <a href="#setting-default-options">Setting default options</a>
    </li>
    <li>
      <a href="#backwards-compatibility">Backwards compatibility</a>
      <ul class="nav">
        <li><a href="#compat-matcher">Simplified function for matching data objects</a></li>
        <li><a href="#initSelection">Old initial selections with <code>initSelection</code></a></li>
        <li><a href="#query">Querying old data with <code>query</code></a></li>
        <li><a href="#input-fallback">Compatibility with <code>&lt;input type="text" /&gt;</code></a></li>
      </ul>
    </li>
  </ul>
  <a class="back-to-top" href="#top">
    Back to top
  </a>
</nav>

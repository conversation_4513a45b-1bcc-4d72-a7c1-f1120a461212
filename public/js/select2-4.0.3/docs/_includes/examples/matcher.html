<section>
  <h1 id="matcher">Customizing how results are matched</h1>

  <p>
    Unlike other dropdowns on this page, this one matches options only if
    the term appears in the beginning of the string as opposed to anywhere:
  </p>

  <p>
    This custom matcher uses a
    <a href="options.html#compat-matcher">compatibility module</a> that is
    only bundled in the
    <a href="index.html#builds-full">full version of Select2</a>. You also
    have the option of using a
    <a href="options.html#matcher">more complex matcher</a>.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-matcher-start js-states form-control"></select>
    </p>
  </div>

{% highlight js linenos %}
function matchStart (term, text) {
  if (text.toUpperCase().indexOf(term.toUpperCase()) == 0) {
    return true;
  }

  return false;
}

$.fn.select2.amd.require(['select2/compat/matcher'], function (oldMatcher) {
  $(".js-example-matcher-start").select2({
    matcher: oldMatcher(matchStart)
  })
});
{% endhighlight %}
</section>

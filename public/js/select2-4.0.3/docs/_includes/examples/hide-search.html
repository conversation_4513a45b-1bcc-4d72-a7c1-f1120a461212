<section>

  <h1 id="hide-search">Hiding the search box</h1>

  <p>
    Select2 allows you to hide the search box depending on the number of
    options which are displayed. In this example, we use the value
    <code>Infinity</code> to tell Select2 to never display the search box.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-basic-hide-search js-states form-control"></select>
    </p>
  </div>

{% highlight js linenos %}
$(".js-example-basic-hide-search").select2({
  minimumResultsForSearch: Infinity
});
{% endhighlight %}
</section>

<section>
  <h1 id="multiple-max">
    Limiting the number of selections
  </h1>

  <p>
    Select2 multi-value select boxes can set restrictions regarding the
    maximum number of options selected. The select below is declared with
    the <code>multiple</code> attribute with <code>maximumSelectionLength</code>
    in the select2 options.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-basic-multiple-limit js-states form-control" multiple="multiple"></select>
    </p>
  </div>

{% highlight js linenos %}
$(".js-example-basic-multiple-limit").select2({
  maximumSelectionLength: 2
});
{% endhighlight %}
</section>

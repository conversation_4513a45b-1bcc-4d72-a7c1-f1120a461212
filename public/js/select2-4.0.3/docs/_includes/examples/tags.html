<section>
  <h1 id="tags">Tagging support</h1>

  <p>
    Select2 can be used to quickly set up fields used for tagging.
  </p>

  <p>
    Note that when tagging is enabled the user can select from pre-existing
    options or create a new tag by picking the first choice, which is what
    the user has typed into the search box so far.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-tags form-control" multiple="multiple">
        <option selected="selected">orange</option>
        <option>white</option>
        <option selected="selected">purple</option>
      </select>
    </p>
  </div>

{% highlight js linenos %}
$(".js-example-tags").select2({
  tags: true
})
{% endhighlight %}
</section>

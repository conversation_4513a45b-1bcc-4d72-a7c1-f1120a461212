<section>
  <h1 id="tokenizer">Automatic tokenization</h1>

  <p>
    Select2 supports ability to add choices automatically as the user is
    typing into the search field. Try typing in the search field below and
    entering a space or a comma.
  </p>

  <p>
    The separators that should be used when tokenizing can be specified
    using the <a href="options.html#tokenSeparators">tokenSeparators</a>
    options.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-tokenizer form-control" multiple="multiple">
        <option>red</option>
        <option>blue</option>
        <option>green</option>
      </select>
    </p>
  </div>

{% highlight js linenos %}
$(".js-example-tokenizer").select2({
  tags: true,
  tokenSeparators: [',', ' ']
})
{% endhighlight %}
</section>

<section>
  <h1 id="placeholders">Placeholders</h1>

  <p>
    A placeholder value can be defined and will be displayed until a
    selection is made. Select2 uses the <code>placeholder</code> attribute
    on multiple select boxes, which requires IE 10+. You can support it in
    older versions with
    <a href="https://github.com/jamesallardice/Placeholders.js">the Placeholders.js polyfill</a>.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-placeholder-single js-states form-control">
        <option></option>
      </select>
    </p>
    <p>
      <select class="js-example-placeholder-multiple js-states form-control" multiple="multiple"></select>
    </p>
  </div>

  <pre data-fill-from=".js-code-placeholder"></pre>

<script type="text/javascript" class="js-code-placeholder">
$(".js-example-placeholder-single").select2({
  placeholder: "Select a state",
  allowClear: true
});

$(".js-example-placeholder-multiple").select2({
  placeholder: "Select a state"
});
</script>

</section>

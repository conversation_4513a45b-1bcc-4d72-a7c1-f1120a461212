<section>

  <h1 id="disabled">Disabled mode</h1>

  <p>
    Select2 will respond to the <code>disabled</code> attribute on
    <code>&lt;select&gt;</code> elements. You can also initialize Select2
    with <code>disabled: true</code> to get the same effect.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-disabled js-states form-control" disabled="disabled"></select>
    </p>

    <p>
      <select class="js-example-disabled-multi js-states form-control" multiple="multiple" disabled="disabled"></select>
    </p>
    <div class="btn-group btn-group-sm" role="group" aria-label="Programmatic enabling and disabling">
      <button type="button" class="js-programmatic-enable btn btn-default">
        Enable
      </button>
      <button type="button" class="js-programmatic-disable btn btn-default">
        Disable
      </button>
    </div>
  </div>

  <pre data-fill-from=".js-code-disabled"></pre>

<script type="text/javascript" class="js-code-disabled">
$(".js-programmatic-enable").on("click", function () {
  $(".js-example-disabled").prop("disabled", false);
  $(".js-example-disabled-multi").prop("disabled", false);
});

$(".js-programmatic-disable").on("click", function () {
  $(".js-example-disabled").prop("disabled", true);
  $(".js-example-disabled-multi").prop("disabled", true);
});
</script>

</section>

<section>
  <h2 id="events-public">
    Public jQuery events
  </h2>

  <h3>
    What events will Select2 trigger?
  </h3>

  <p>
    Select2 will trigger a few different events when different actions are taken using the component, allowing you to add custom hooks and perform actions.
  </p>

  <dl class="s2-docs-panels">
    <dt>change</dt>
    <dd>Triggered whenever an option is selected or removed.</dd>

    <dt>select2:close</dt>
    <dd>Triggered whenever the dropdown is closed.</dd>

    <dt>select2:closing</dt>
    <dd>Triggered before the dropdown is closed. This event can be prevented.</dd>

    <dt>select2:open</dt>
    <dd>Triggered whenever the dropdown is opened.</dd>

    <dt>select2:opening</dt>
    <dd>Triggered before the dropdown is opened. This event can be prevented.</dd>

    <dt>select2:select</dt>
    <dd>Triggered whenever a result is selected.</dd>

    <dt>select2:selecting</dt>
    <dd>Triggered before a result is selected. This event can be prevented.</dd>

    <dt>select2:unselect</dt>
    <dd>Triggered whenever a selection is removed.</dd>

    <dt>select2:unselecting</dt>
    <dd>Triggered before a selection is removed. This event can be prevented.</dd>
  </dl>

  <h3>
    Does Select2 include extra information in these events?
  </h3>

  {% include options/not-written.html %}

  <h3>
    How can I attach listeners for these events?
  </h3>

{% highlight js linenos %}
$('select').on('select2:select', function (evt) {
  // Do something
});
{% endhighlight %}

  {% include options/not-written.html %}

  <h3>
    What events does Select2 listen for?
  </h3>

  <p>
    Select2 will listen for the <code>change</code> event on the
    <code>&lt;select&gt;</code> that it is attached to. If you make any
    external changes that need to be reflected in Select2 (such as changing the
    value), you should trigger this event.
  </p>

{% highlight js linenos %}
$('select').val('US'); // Select the option with a value of 'US'
$('select').trigger('change'); // Notify any JS components that the value changed
{% endhighlight %}

  <h3>
    Can I trigger an event other than <code>change</code> to notify Select2 of changes?
  </h3>

  <p>
    It's common for other components to be listening to the <code>change</code>
    event, or for custom event handlers to be attached that may have side
    effects. Select2 does not have a custom event (like
    <code>select2:update</code>) that can be triggered other than
    <code>change</code>. You can rely on jQuery's event namespacing to limit
    the scope to Select2 though by triggering the <code>change.select2</code>
    event.
  </p>

{% highlight js linenos %}
$('select').val('US'); // Change the value or make some change to the internal state
$('select').trigger('change.select2'); // Notify only Select2 of changes
{% endhighlight %}

  <h3>
    What events can be prevented? How can I prevent a selection from being made?
  </h3>

  {% include options/not-written.html %}
</section>
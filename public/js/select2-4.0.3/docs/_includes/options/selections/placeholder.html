<section>
  <h2 id="placeholder">
    How can I have Select2 display a placeholder?
  </h2>

  <p>
    Select2 supports displaying a placeholder by default using the <code>placeholder</code> option. This can be either a data object matching the placeholder option, or a string to display as the placeholder if you are using a blank placeholder option.
  </p>

{% highlight js linenos %}
$('select').select2({
  placeholder: 'Select an option'
});
{% endhighlight %}

  <h3>
    My first option is being displayed instead of my placeholder
  </h3>

  <p>
    This usually means that you do not have a blank <code>&lt;option&gt;&lt/option&gt;</code> as the first option in your <code>&lt;select&gt;</code>.
  </p>

  <p>
    Note that this does not apply to multiple selects, as the browser does not select the first option by default when multiple selections can be made.
  </p>

  <h3>
    I am using AJAX, can I still show a placeholder?
  </h3>

  <p>
    Yes, Select2 supports placeholders for all configurations. You will still need to add in the placeholder option if you are using a single select.
  </p>

  <h3>
    Can I use an option without a blank value as my placeholder?
  </h3>

  <p>
    The <code>placeholder</code> option allows you to pass in a data object instead of just a string if you need more flexibility. The <code>id</code> of the data object should match the <code>value</code> of the placeholder option.
  </p>

{% highlight js linenos %}
$('select').select2({
  placeholder: {
    id: '-1', // the value of the option
    text: 'Select an option'
  }
});
{% endhighlight %}

  <h3>
    Can I change how the placeholder looks?
  </h3>

  <p>
    When using Select2 <strong>when only a single selection can be made</strong>, the placeholder option will be passed through the standard templating methods, including the <code>templateSelection</code> option, so you are able to change how it is displayed.
  </p>

{% highlight js linenos %}
$('select').select2({
  templateResult: function (data) {
    if (data.id === '') { // adjust for custom placeholder values
      return 'Custom styled placeholder text';
    }

    return data.text;
  }
});
{% endhighlight %}

  <p>
    <strong>When multiple selections are allowed</strong>, the placeholder will be displayed using the <code>placeholder</code> attribute on the search box. You can cusotmize the display of this placholder using CSS, as explained in the following Stack Overflow answer: <a href="http://stackoverflow.com/q/2610497/359284">Change an input's HTML5 placeholder color with CSS</a>
  </p>

  <h3>
    My placeholders aren&apos;t being displayed in Internet Explorer
  </h3>

  <p>
    Select2 uses the native <code>placeholder</code> attribute on input boxes for the multiple select, and that attribute is not supported in older versions of Internet Explorer. You need to include <a href="https://github.com/jamesallardice/Placeholders.js">Placeholders.js</a> on your page, or use the full build, in order to add <code>placeholder</code> attribute support to input boxes.
  </p>
</section>

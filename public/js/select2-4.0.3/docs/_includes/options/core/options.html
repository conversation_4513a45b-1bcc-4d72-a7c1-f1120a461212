<section>
  <h2 id="options">
    How should Select2 be initialized?
  </h2>

  <p>
    Select2 will register itself as a jQuery function if you use any of the distribution builds, so you can call <code>.select2()</code> on any jQuery element where you would like to initialize Select2.
  </p>

{% highlight js linenos %}
$('select').select2();
{% endhighlight %}

  <p>
    You can optionally pass an object containing all of the options that you would like to initialize Select2 with.
  </p>

{% highlight js linenos %}
$('select').select2({
  placeholder: 'Select an option'
});
{% endhighlight %}

  <h3 id="setting-default-options">
    Can default options be set for all dropdowns?
  </h3>

  <p>
    In some cases, you need to set the default options for all instances of
    Select2 in your web application. This is especially useful when you are
    migrating from past versions of Select2, or you are using non-standard
    options <a href="#amd">like custom AMD builds</a>. Select2 exposes the
    default options through <code>$.fn.select2.defaults</code>, which allows
    you to set them globally.
  </p>

  <p>
    When setting options globally, any past defaults that have been set will
    be overriden. Default options are only used when an option is requested
    that has not been set during initialization.
  </p>

  <p>
    <strong>You can set default options</strong> by calling
    <code>$.fn.select2.defaults.set("key", "value")</code>.
  </p>

{% highlight js linenos %}
$.fn.select2.defaults.set("theme", "classic");
{% endhighlight %}

  <h3>
    How can I set a default value for a nested option?
  </h3>

  <p>
    The key that is
    set should take the same format as keys set using
    <a href="#data-attributes">HTML <code>data-*</code> attributes</a> which
    means that two dashes (<code>--</code>) will be replaced by a level of
    nesting, and a single dash (<code>-</code>) will convert it to a camelCase
    string.
  </p>

{% highlight js linenos %}
$.fn.select2.defaults.set("ajax--cache", false);
{% endhighlight %}

  <h3>
    How can I reset all of the global default options?
  </h3>

  <p>
    You can reset the default options to their initial values by calling
  </p>

{% highlight js linenos %}
$.fn.select2.defaults.reset();
{% endhighlight %}
</section>
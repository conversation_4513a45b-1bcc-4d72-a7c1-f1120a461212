<section>
  <h2 id="amd">
    Can I use Select2 with my AMD or CommonJS loader?
  </h2>

  <p>
    Select2 should work with most AMD- or CommonJS-compliant module loaders, including <a href="http://requirejs.org/">RequireJS</a> and <a href="https://github.com/jrburke/almond">almond</a>. Select2 ships with a modified version of the <a href="https://github.com/umdjs/umd/blob/f208d385768ed30cd0025d5415997075345cd1c0/templates/jqueryPlugin.js">UMD jQuery template</a> that supports both CommonJS and AMD environments.
  </p>

  <h3>
    How do I tell Select2 where to look for modules?
  </h3>

  <p>
    For most AMD and CommonJS setups, the location of the data files used by Select2 will be automatically determined and handled without you needing to do anything.
  </p>

  <p>
    If you are using Select2 in a build environment where preexisting module names are changed during a build step, Select2 may not be able to find optional dependencies or language files. You can manually set the prefixes to use for these files using the <code>amdBase</code> and <code>amdLanugageBase</code> options.
  </p>

{% highlight js linenos %}
$.fn.select2.defaults.set('amdBase', 'select2/');
$.fn.select2.defaults.set('amdLanguageBase', 'select2/i18n/');
{% endhighlight %}

  <h3>
    Select2 is being placed before jQuery in my JavaScript file
  </h3>

  <p>
    Due to <a href="https://github.com/jrburke/requirejs/issues/1342">a bug in older versions</a> of the r.js build tool, Select2 was sometimes placed before jQuery in then compiled build file. Because of this, Select2 will trigger an error because it won't be able to find or load jQuery.
  </p>

  <p>
    By upgrading to version 2.1.18 or higher of the r.js build tool, you will be able to fix the issue.
  </p>

  <h3>
    Should I point to the files in <code>dist</code> or <code>src</code>?
  </h3>

  <p>
    Select2 internally uses AMD and the r.js build tool to build the files located in the <code>dist</code> folder. These are built using the files in the <code>src</code> folder, so <em>you can</em> just point your modules to the Select2 source and load in <code>jquery.select2</code> or <code>select2/core</code> when you want to use Select2. The files located in the <code>dist</code> folder are also AMD-compatible, so you can point to that file if you want to load in all of the default Select2 modules.
  </p>
</section>
<section>
  <h2 id="query">
    Querying old data with <code>query</code>
  </h2>

  <p class="alert alert-warning">
    <a href="announcements-4.0.html#query-to-data-adapter" class="alert-link">Deprecated in Select2 4.0.</a>
    This has been replaced by another option and is only available in the
    <a href="index.html#builds-full" class="alert-link">full builds</a> of
    Select2.
  </p>

  <p>
    In the past, Select2 supported an option called <code>query</code> that
    allowed for a custom data source to be used. This option has been replaced
    by the <code>query</code> method on the
    <a href="#dataAdapter">data adapter</a> and takes a very similar set of
    parameters.
  </p>

  <div class="row">
    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Key</dt>
        <dd>
          <code>query</code>
        </dd>

        <dt>Value</dt>
        <dd>
          A function taking <code>params</code> (including a <code>callback</code>)
        </dd>
      </dl>
    </div>

    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Adapter</dt>
        <dd>
          <code title="select2/data/base">DataAdapter</code>
        </dd>

        <dt>Decorator</dt>
        <dd>
          <code title="select2/compat/query">Query</code>
        </dd>
      </dl>
    </div>
  </div>
</section>
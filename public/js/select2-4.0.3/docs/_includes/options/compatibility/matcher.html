<section>
  <h2 id="compat-matcher">
    Simplified function for matching data objects
  </h2>

  <p class="alert alert-info">
    <a href="announcements-4.0.html#new-matcher" class="alert-link">Added in Select2 4.0.0.</a>
    This method was added to make upgrading easier from earlier versions of
    Select2.
  </p>

  <p>
    During the <a href="announcements-4.0.html">Select2 4.0.0 release</a>, the
    <code>matcher</code> function was changed to allow for more complex
    matching of nested objects.
  </p>

  <div class="row">
    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Key</dt>
        <dd>
          <code>matcher</code>
        </dd>

        <dt>Value</dt>
        <dd>
          A function taking a search <code>term</code> and the data object
          <code>text</code>.
        </dd>
      </dl>
    </div>

    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Adapter</dt>
        <dd>
          <code title="select2/compat/matcher">oldMatcher</code>
        </dd>
      </dl>
    </div>
  </div>

  <p>
    The <a href="examples.html#matcher">custom matcher example</a> provides a
    guide for how to use this in your own application. For those upgrading
    from older versions of Select2, you just need to wrap your old
    <code>matcher</code> with this function to maintain compatibility.
  </p>
</section>
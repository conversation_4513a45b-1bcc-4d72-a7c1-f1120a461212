<section>
  <h2>
    Can I change when search results are loaded?
  </h2>

  <h3>
    Can Select2 wait until the user has typed a search term before triggering the request?
  </h3>

{% highlight js linenos %}
$('select').select2({
  ajax: {
    delay: 250 // wait 250 milliseconds before triggering the request
  }
});
{% endhighlight %}

  {% include options/not-written.html %}

  <h3>
    Select2 is allowing long search terms, can this be prevented?
  </h3>

{% highlight js linenos %}
$('select').select2({
  maximumInputLength: 20 // only allow terms up to 20 characters long
});
{% endhighlight %}

  {% include options/not-written.html %}

  <h3>
    I only want the search box if there are enough results
  </h3>

{% highlight js linenos %}
$('select').select2({
  minimumResultsForSearch: 20 // at least 20 results must be displayed
});
{% endhighlight %}

  {% include options/not-written.html %}

  <h3>
    How can I permanently hide the search box?
  </h3>

{% highlight js linenos %}
$('select').select2({
  minimumResultsForSearch: Infinity
});
{% endhighlight %}

  {% include options/not-written.html %}
</section>
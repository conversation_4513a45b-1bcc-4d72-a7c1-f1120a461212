<section>
  <h2>
    Can options be created based on the search term?
  </h2>

  <h3>
    How do I enable tagging?
  </h3>

{% highlight js linenos %}
$('select').select2({
  tags: true
});
{% endhighlight %}

  {% include options/not-written.html %}

  <h3>
    Does tagging work with a single select?
  </h3>

  <p>
    Yes.
  </p>

  {% include options/not-written.html %}

  <h3>
    How do I add extra properties to the tag?
  </h3>

{% highlight js linenos %}
$('select').select2({
  createTag: function (params) {
    var term = $.trim(params.term);

    if (term === '') {
      return null;
    }

    return {
      id: term,
      text: term,
      newTag: true // add additional parameters
    }
  }
});
{% endhighlight %}

  {% include options/not-written.html %}

  <h3>
    Can I control when tags are created?
  </h3>

{% highlight js linenos %}
$('select').select2({
  createTag: function (params) {
    // Don't offset to create a tag if there is no @ symbol
    if (params.term.indexOf('@') === -1) {
      // Return null to disable tag creation
      return null;
    }

    return {
      id: params.term,
      text: params.term
    }
  }
});
{% endhighlight %}

  {% include options/not-written.html %}

  <h3>
    How do I control the placement of the option?
  </h3>

{% highlight js linenos %}
$('select').select2({
  insertTag: function (data, tag) {
    // Insert the tag at the end of the results
    data.push(tag);
  }
});
{% endhighlight %}

  {% include options/not-written.html %}
</section>
/*********** Global ************/
body {
    font-family: 'Cairo', sans-serif;
    margin-top: 51px;
}

.my-navbar {
    margin-bottom: 0;
}

/*********** End Global ************/

/*********** Home ************/

.home .banner {
    background-image: url("../imgs/bg_eventcamp.jpg");
    color : #fff;
    padding: 0 0 0 0;
}

.home .banner .top-content {
    background-color: rgba(67, 163, 238, 0.30);
    padding: 50px 0 50px 0;
}

/*.home .top-content .seminar-title {*/
    /*color : #43a3ee;*/
/*}*/

.seminar-title {

    border: 1px solid;

    display: inline-block;

    padding: 20px;

}

.home .banner .bottom-content {
    background-color: rgba(0 , 0 , 0 , .3);
    padding: 20px 0 20px 0;
}

.home .banner .bottom-content h3 {
    margin: 0;
}

.home .banner .bottom-content .title {
    margin-top: 17px;
}

.home .register {
    /*background-color: rgba(0 , 0 , 0 , .9);*/
    /*color: #fff;*/
    /*padding-bottom: 50px;*/
    /*padding-top: 50px;*/
}

.home .register .form-container {
    background-color: #efefef;
    color: #000;
    padding: 50px;
}

.home .description {
    background-color: #000;
    color : #fff;
    padding-bottom: 50px;
    padding-top: 50px;
}

.home .lecturer .title {
    margin-bottom: 40px;
    margin-top: 0;
    padding-top: 50px;
}

.home .lecturer .lecturer-img {
    padding: 5px;
    border: 1px solid #cdcdcd;
}

.home .lecturer .schedule {
    margin-top: 50px;
    border-top: 1px solid #000;
}

.home .lecturer .schedule .title {
    margin-bottom: 40px;
}

.home .lecturer .schedule .table .schedule-right-title {
    color : #1b6d85;
}

.home .lecturer .schedule .table p {
    margin: 0;
}

.home .map .title {
    margin-bottom: 40px;
    margin-top: 40px;
    border-top: 1px solid #000;
    padding-top: 20px;
}

.home .footer {
    background-color: #000;
    color: #fff;
    padding: 50px 0 50px 0;
}

/*********** End Home ************/
